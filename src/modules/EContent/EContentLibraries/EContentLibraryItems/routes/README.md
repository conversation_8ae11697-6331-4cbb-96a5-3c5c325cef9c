# EContent Item Content Form Route

## Overview

This directory contains route components that allow EContentItemContentForm to be accessed outside of the ItemsView context.

## EContentItemContentFormRoute

### Purpose
The `EContentItemContentFormRoute` component provides a standalone route for editing EContent item content without being embedded within the ItemsView. This allows for:

- Direct access to content editing via URL
- Better user experience when navigating to content from external sources
- Cleaner URL structure for content editing
- Avoids conflicts with existing ItemsView routing

### Route Pattern
```
/e-content/libraries/edit/:libraryId/content-form/:itemId/:contentId
```

### Parameters
- `libraryId`: The ID of the EContent library
- `itemId`: The ID of the EContent item
- `contentId`: The ID of the content to edit, or "new" for creating new content

### Usage Examples

#### Edit existing content:
```
/e-content/libraries/edit/123/content-form/456/789
```

#### Create new content:
```
/e-content/libraries/edit/123/content-form/456/new
```

### Features
- Loads content data for existing content
- Loads item data to get resource information
- <PERSON>les both create and update operations
- Provides proper navigation back to appropriate views
- Supports content from different pages (contentview, itemsview)
- Uses a unique route pattern to avoid conflicts with existing routing

### Navigation
The component handles navigation back to:
- Content view if accessed from content view (`fromPage: 'contentview'`)
- Item contents list for other cases

### Dependencies
- `eContentItemContent.graphql` - Query for existing content
- `eContentItem.graphql` - Query for item and resource data
- `createEContentItemContent.graphql` - Mutation for creating content
- `updateEContentItemContent.graphql` - Mutation for updating content

### Integration
The route is integrated into the main EContent routing structure in `EContentLibraryItemsTable.tsx` and uses a unique pattern (`content-form`) to avoid conflicts with existing ItemsView routing patterns.

### Migration from Old URL Pattern
If you were previously using URLs like:
```
/e-content/libraries/edit/563/edit/13504/contents/edit/57041/contents
```

You should now use:
```
/e-content/libraries/edit/563/content-form/13504/57041
```

This new pattern ensures the content form opens outside of ItemsView as intended.
